const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction, createEditMessage } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

const router = express.Router();

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let vacationsTableCreated = false;

// إنشاء جدول الإجازات إذا لم يكن موجودًا
const setupVacationsTable = async () => {
  if (vacationsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً
  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS vacations (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) DEFAULT NULL,
        employee_name varchar(255) DEFAULT NULL,
        department varchar(255) NOT NULL,
        vacation_type enum('casual','permission','absence','annual','unpaid','sick','official') NOT NULL,
        official_type varchar(50) DEFAULT NULL,
        vacation_date date NOT NULL,
        days_count int NOT NULL DEFAULT '1',
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_department (department),
        KEY idx_vacation_date (vacation_date),
        KEY idx_vacation_type (vacation_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `);

    // التحقق من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
    const [columns] = await pool.promise().query("SHOW COLUMNS FROM vacations");
    const columnNames = columns.map(col => col.Field);

    // إضافة عمود department إذا لم يكن موجودًا
    if (!columnNames.includes('department')) {
      await pool.promise().query(
        "ALTER TABLE vacations ADD COLUMN department varchar(255) NOT NULL AFTER employee_name"
      );
    }

    // إضافة عمود days_count إذا لم يكن موجودًا
    if (!columnNames.includes('days_count')) {
      await pool.promise().query(
        "ALTER TABLE vacations ADD COLUMN days_count int NOT NULL DEFAULT '1' AFTER vacation_date"
      );
    }

    // إضافة عمود official_type إذا لم يكن موجودًا
    if (!columnNames.includes('official_type')) {
      await pool.promise().query(
        "ALTER TABLE vacations ADD COLUMN official_type varchar(50) DEFAULT NULL AFTER vacation_type"
      );
    }

    vacationsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول الإجازات:', error);
    throw error;
  }
};

// إنشاء جدول الإجازات
router.get('/setup-vacations-table', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await setupVacationsTable();
    res.json({ message: 'تم إنشاء جدول الإجازات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول الإجازات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول الإجازات' });
  }
});

// الحصول على إجازات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const { employee_code } = req.params;

    const [rows] = await pool.promise().query(
      `SELECT id, employee_code, employee_name, department, vacation_type, official_type,
              vacation_date, days_count, created_at,
              DATE_FORMAT(vacation_date, '%Y-%m-%d') as vacation_date_formatted
       FROM vacations
       WHERE employee_code = ?
       ORDER BY vacation_date DESC`,
      [employee_code]
    );

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب إجازات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب إجازات الموظف' });
  }
});

// الحصول على إجازات قسم محدد
router.get('/department/:department', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const { department } = req.params;

    const [rows] = await pool.promise().query(`
      SELECT id, employee_code, employee_name, department, vacation_type, official_type,
             vacation_date, days_count, created_at,
             DATE_FORMAT(vacation_date, '%Y-%m-%d') as vacation_date_formatted
      FROM vacations
      WHERE department = ?
      ORDER BY vacation_date DESC
    `, [department]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب إجازات القسم:', error);
    res.status(500).json({ error: 'فشل في جلب إجازات القسم' });
  }
});

// الحصول على الإجازات في فترة زمنية محددة
router.get('/date-range', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    if (!start_date || !end_date) {
      return res.status(400).json({ error: 'يرجى توفير تاريخ البداية والنهاية' });
    }

    const [rows] = await pool.promise().query(`
      SELECT id, employee_code, employee_name, department, vacation_type, official_type,
             vacation_date, days_count, created_at,
             DATE_FORMAT(vacation_date, '%Y-%m-%d') as vacation_date_formatted
      FROM vacations
      WHERE vacation_date BETWEEN ? AND ?
      ORDER BY vacation_date DESC
    `, [start_date, end_date]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الإجازات في الفترة المحددة:', error);
    res.status(500).json({ error: 'فشل في جلب الإجازات في الفترة المحددة' });
  }
});

// إضافة إجازة جديدة
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await setupVacationsTable();

    const {
      employee_code,
      vacation_type,
      vacation_date,
      days_count = 1,
      official_type = null
    } = req.body;

    if (!employee_code || !vacation_type || !vacation_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب أن تكون موجودة' });
    }

    // التحقق من صحة نوع الإجازة
    const validVacationTypes = ['casual', 'permission', 'absence', 'annual', 'unpaid', 'sick', 'official'];
    if (!validVacationTypes.includes(vacation_type)) {
      return res.status(400).json({ error: 'نوع الإجازة غير صحيح' });
    }

    // الحصول على بيانات الموظف
    const [employeeRows] = await pool.promise().query(
      "SELECT full_name, department FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeRows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const employee_name = employeeRows[0].full_name;
    const department = employeeRows[0].department;

    // التحقق من عدم وجود إجازة مكررة في نفس التاريخ
    const [existingVacations] = await pool.promise().query(
      `SELECT id, vacation_type, vacation_date
       FROM vacations
       WHERE employee_code = ? AND vacation_date = ?`,
      [employee_code, vacation_date]
    );

    if (existingVacations.length > 0) {
      const existing = existingVacations[0];
      return res.status(400).json({
        error: `يوجد إجازة أخرى للموظف ${employee_name} في تاريخ ${existing.vacation_date}. لا يمكن إضافة إجازات مكررة.`,
        existing_vacation: {
          id: existing.id,
          type: existing.vacation_type,
          vacation_date: existing.vacation_date
        }
      });
    }

    const [result] = await pool.promise().query(
      `INSERT INTO vacations (
        employee_code, employee_name, department, vacation_type, official_type,
        vacation_date, days_count
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        employee_code, employee_name, department, vacation_type, official_type,
        vacation_date, days_count
      ]
    );

    // تسجيل النشاط
    const vacationTypeLabels = {
      'casual': 'إجازة عارضة',
      'permission': 'غياب بإذن',
      'absence': 'غياب بدون إذن',
      'annual': 'إجازة سنوية',
      'unpaid': 'إجازة بدون راتب',
      'sick': 'إجازة مرضية',
      'official': 'إجازات خارج الرصيد'
    };

    const vacationTypeLabel = vacationTypeLabels[vacation_type] || vacation_type;
    let activityMessage = `تم إضافة ${vacationTypeLabel} للموظف: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - التاريخ: ${vacation_date} - عدد الأيام: ${days_count}`;

    if (vacation_type === 'official' && official_type) {
      activityMessage += ` - نوع الإجازة: ${official_type}`;
    }

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'vacations',
      record_id: result.insertId.toString(),
      message: activityMessage
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      employee_name,
      department,
      vacation_type,
      official_type,
      vacation_date,
      days_count,
      message: 'تم إضافة الإجازة بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة الإجازة:', error);
    res.status(500).json({ error: 'فشل في إضافة الإجازة' });
  }
});

// تحديث إجازة
router.put('/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { id } = req.params;

    // تنظيف البيانات المرسلة من العميل
    const updateData = cleanUpdateData(req.body);

    // التحقق من وجود الإجازة
    const [existingVacation] = await pool.promise().query(
      "SELECT * FROM vacations WHERE id = ?",
      [id]
    );

    if (existingVacation.length === 0) {
      return res.status(404).json({ error: 'الإجازة غير موجودة' });
    }

    // التحقق من صحة نوع الإجازة إذا تم تحديثه
    if (updateData.vacation_type) {
      const validVacationTypes = ['casual', 'permission', 'absence', 'annual', 'unpaid', 'sick', 'official'];
      if (!validVacationTypes.includes(updateData.vacation_type)) {
        return res.status(400).json({ error: 'نوع الإجازة غير صحيح' });
      }
    }

    // التحقق من التداخل مع إجازات أخرى (إذا تم تغيير التاريخ أو الموظف)
    if (updateData.employee_code && updateData.vacation_date) {
      const [conflictingVacations] = await pool.promise().query(
        `SELECT id, vacation_type, vacation_date, employee_name
         FROM vacations
         WHERE employee_code = ?
         AND id != ?
         AND vacation_date = ?`,
        [
          updateData.employee_code,
          id,
          updateData.vacation_date
        ]
      );

      if (conflictingVacations.length > 0) {
        const conflict = conflictingVacations[0];
        return res.status(400).json({
          error: `يوجد إجازة أخرى للموظف في تاريخ ${conflict.vacation_date}. لا يمكن تحديث الإجازة لتتداخل مع إجازة أخرى.`,
          conflicting_vacation: {
            id: conflict.id,
            type: conflict.vacation_type,
            vacation_date: conflict.vacation_date
          }
        });
      }
    }

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;
    delete updateData.created_at;

    // إذا تم تغيير كود الموظف، تحديث اسم الموظف والقسم
    if (updateData.employee_code && updateData.employee_code !== existingVacation[0].employee_code) {
      const [employeeRows] = await pool.promise().query(
        "SELECT full_name, department FROM employees WHERE code = ?",
        [updateData.employee_code]
      );

      if (employeeRows.length > 0) {
        updateData.employee_name = employeeRows[0].full_name;
        updateData.department = employeeRows[0].department;
      }
    }

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    if (updateData.hasOwnProperty('vacation_date')) {
      if (updateData.vacation_date === '' || updateData.vacation_date === null || updateData.vacation_date === undefined) {
        updateData.vacation_date = null;
      }
    }

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(id);

    await pool.promise().query(
      `UPDATE vacations SET ${setClause} WHERE id = ?`,
      values
    );

    // تسجيل النشاط
    const oldData = existingVacation[0];
    const newData = { ...oldData, ...updateData };

    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'القسم',
      vacation_type: 'نوع الإجازة',
      official_type: 'نوع الإجازة الرسمية',
      vacation_date: 'تاريخ الإجازة',
      days_count: 'عدد الأيام'
    };

    const editMessage = createEditMessage(
      `إجازة للموظف: ${newData.employee_name || newData.employee_code}`,
      oldData,
      newData,
      fieldLabels
    );

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'vacations',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث الإجازة بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الإجازة:', error);
    res.status(500).json({ error: 'فشل في تحديث الإجازة' });
  }
});

// حذف إجازة
router.delete('/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على بيانات الإجازة قبل الحذف لتسجيل النشاط
    const [vacationRows] = await pool.promise().query(
      "SELECT * FROM vacations WHERE id = ?",
      [id]
    );

    if (vacationRows.length === 0) {
      return res.status(404).json({ error: 'الإجازة غير موجودة' });
    }

    const vacation = vacationRows[0];

    const [result] = await pool.promise().query(
      "DELETE FROM vacations WHERE id = ?",
      [id]
    );

    // تسجيل النشاط
    const vacationTypeLabels = {
      'casual': 'إجازة عارضة',
      'permission': 'غياب بإذن',
      'absence': 'غياب بدون إذن',
      'annual': 'إجازة سنوية',
      'unpaid': 'إجازة بدون راتب',
      'sick': 'إجازة مرضية',
      'official': 'إجازة رسمية'
    };

    const vacationTypeLabel = vacationTypeLabels[vacation.vacation_type] || vacation.vacation_type;
    let activityMessage = `تم حذف ${vacationTypeLabel} للموظف: ${vacation.employee_name} (كود: ${vacation.employee_code}) - القسم: ${vacation.department} - التاريخ: ${vacation.vacation_date} - عدد الأيام: ${vacation.days_count}`;

    if (vacation.vacation_type === 'official' && vacation.official_type) {
      activityMessage += ` - نوع الإجازة الرسمية: ${vacation.official_type}`;
    }

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'vacations',
      record_id: id.toString(),
      message: activityMessage
    });

    res.json({ message: 'تم حذف الإجازة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الإجازة:', error);
    res.status(500).json({ error: 'فشل في حذف الإجازة' });
  }
});

// الحصول على جميع الإجازات
router.get('/', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(`
      SELECT id, employee_code, employee_name, department, vacation_type, official_type,
             vacation_date, days_count, created_at,
             DATE_FORMAT(vacation_date, '%Y-%m-%d') as vacation_date_formatted
      FROM vacations
      ORDER BY vacation_date DESC
    `);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الإجازات:', error);
    res.status(500).json({ error: 'فشل في جلب الإجازات' });
  }
});

module.exports = router;