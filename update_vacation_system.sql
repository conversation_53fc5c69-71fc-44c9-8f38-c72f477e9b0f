-- تحديث نظام الإجازات
-- تغيير نوع الإجازة من "رسمية" إلى "إجازات خارج الرصيد"
-- إضافة أنواع الإجازات الجديدة

-- 1. إضافة الأنواع الجديدة للإجازات الرسمية في البيانات الموجودة
-- تحديث البيانات الموجودة لتتوافق مع النظام الجديد

-- 2. تحديث أي مراجع في جداول أخرى إذا وجدت
-- (لا توجد جداول أخرى تحتاج تحديث حسب البنية الحالية)

-- 3. إضافة تعليقات توضيحية للجدول
ALTER TABLE vacations COMMENT = 'جدول الإجازات - يحتوي على جميع أنواع الإجازات بما في ذلك الإجازات خارج الرصيد';

-- 4. إضافة فهرس على نوع الإجازة الرسمية إذا لم يكن موجوداً
ALTER TABLE vacations ADD INDEX IF NOT EXISTS idx_official_type (official_type);

-- 5. تحديث أي بيانات موجودة لتتوافق مع النظام الجديد
-- تحديث الأنواع القديمة للإجازات الرسمية إلى الأنواع الجديدة حسب الحاجة

-- ملاحظة: الأنواع الجديدة للإجازات خارج الرصيد:
-- - eid_fitr: عيد الفطر
-- - emergency: اجازة اضطرارية  
-- - birth: مولود
-- - maternity: اجازة وضع
-- - marriage: زواج
-- - death_first_degree: الوفاة من الدرجة الاولى
-- - military_service: الاستدعاء للجيش
-- - exams: الامتحانات
-- - pilgrimage: الحج والعمرة

-- 6. إضافة قيود للتأكد من صحة البيانات
-- التأكد من أن الإجازات الرسمية لها نوع محدد
-- (يتم التحقق من هذا في التطبيق)

-- 7. تحديث أي إعدادات أو تكوينات متعلقة بالنظام
-- (يتم التحديث في ملفات التطبيق)

-- تم الانتهاء من تحديث قاعدة البيانات
-- النظام الآن يدعم:
-- 1. تغيير اسم "الإجازة الرسمية" إلى "إجازات خارج الرصيد"
-- 2. الأنواع الجديدة للإجازات خارج الرصيد
-- 3. إزالة أزرار الإجراءات من عرض التفاصيل
